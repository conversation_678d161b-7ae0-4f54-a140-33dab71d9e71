2025-07-22 13:32:26,458 - __main__ - INFO - Starting up Search API...
2025-07-22 13:32:26,459 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 13:32:26,463 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 13:32:26,464 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 13:32:30,079 - __main__ - INFO - Model loaded successfully
2025-07-22 13:32:30,080 - __main__ - INFO - Initializing database connection...
2025-07-22 13:32:30,223 - __main__ - INFO - Database connection established
2025-07-22 13:32:30,225 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 13:32:30,225 - __main__ - INFO - Cache file missing: search_cache/embeddings.dat
2025-07-22 13:32:30,226 - __main__ - INFO - Cache files not found, generating new cache from database...
2025-07-22 13:32:30,226 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-07-22 13:32:30,734 - __main__ - INFO - Total records to process: 393297
2025-07-22 13:32:30,737 - __main__ - INFO - Processing chunk 1: records 0-5000
2025-07-22 13:32:34,888 - __main__ - INFO - Progress: 5000/393297 records processed (1.3%)
2025-07-22 13:32:34,890 - __main__ - INFO - Processing chunk 2: records 5000-10000
2025-07-22 13:32:39,792 - __main__ - INFO - Processing chunk 3: records 10000-15000
2025-07-22 13:32:44,142 - __main__ - INFO - Processing chunk 4: records 15000-20000
2025-07-22 13:32:48,126 - __main__ - INFO - Processing chunk 5: records 20000-25000
2025-07-22 13:32:52,520 - __main__ - INFO - Processing chunk 6: records 25000-30000
2025-07-22 13:32:57,375 - __main__ - INFO - Progress: 30000/393297 records processed (7.6%)
2025-07-22 13:32:57,375 - __main__ - INFO - Processing chunk 7: records 30000-35000
2025-07-22 13:33:01,965 - __main__ - INFO - Processing chunk 8: records 35000-40000
2025-07-22 13:33:06,368 - __main__ - INFO - Processing chunk 9: records 40000-45000
2025-07-22 13:33:10,938 - __main__ - INFO - Processing chunk 10: records 45000-50000
2025-07-22 13:33:16,754 - __main__ - INFO - Processing chunk 11: records 50000-55000
2025-07-22 13:33:21,957 - __main__ - INFO - Progress: 55000/393297 records processed (14.0%)
2025-07-22 13:33:21,958 - __main__ - INFO - Processing chunk 12: records 55000-60000
2025-07-22 13:33:26,657 - __main__ - INFO - Processing chunk 13: records 60000-65000
2025-07-22 13:33:31,240 - __main__ - INFO - Processing chunk 14: records 65000-70000
2025-07-22 13:33:36,674 - __main__ - INFO - Processing chunk 15: records 70000-75000
2025-07-22 13:33:41,882 - __main__ - INFO - Processing chunk 16: records 75000-80000
2025-07-22 13:33:46,617 - __main__ - INFO - Progress: 80000/393297 records processed (20.3%)
2025-07-22 13:33:46,618 - __main__ - INFO - Processing chunk 17: records 80000-85000
2025-07-22 13:33:52,259 - __main__ - INFO - Processing chunk 18: records 85000-90000
2025-07-22 13:33:57,158 - __main__ - INFO - Processing chunk 19: records 90000-95000
2025-07-22 13:34:02,771 - __main__ - INFO - Processing chunk 20: records 95000-100000
2025-07-22 13:34:07,882 - __main__ - INFO - Processing chunk 21: records 100000-105000
2025-07-22 13:34:13,204 - __main__ - INFO - Progress: 105000/393297 records processed (26.7%)
2025-07-22 13:34:13,205 - __main__ - INFO - Processing chunk 22: records 105000-110000
2025-07-22 13:34:19,935 - __main__ - INFO - Processing chunk 23: records 110000-115000
2025-07-22 13:34:26,110 - __main__ - INFO - Processing chunk 24: records 115000-120000
2025-07-22 13:34:31,873 - __main__ - INFO - Processing chunk 25: records 120000-125000
2025-07-22 13:34:37,774 - __main__ - INFO - Processing chunk 26: records 125000-130000
2025-07-22 13:34:43,926 - __main__ - INFO - Progress: 130000/393297 records processed (33.1%)
2025-07-22 13:34:43,927 - __main__ - INFO - Processing chunk 27: records 130000-135000
2025-07-22 13:34:50,063 - __main__ - INFO - Processing chunk 28: records 135000-140000
2025-07-22 13:34:57,310 - __main__ - INFO - Processing chunk 29: records 140000-145000
2025-07-22 13:35:03,364 - __main__ - INFO - Processing chunk 30: records 145000-150000
2025-07-22 13:35:09,778 - __main__ - INFO - Processing chunk 31: records 150000-155000
2025-07-22 13:35:17,265 - __main__ - INFO - Progress: 155000/393297 records processed (39.4%)
2025-07-22 13:35:17,266 - __main__ - INFO - Processing chunk 32: records 155000-160000
2025-07-22 13:35:24,222 - __main__ - INFO - Processing chunk 33: records 160000-165000
2025-07-22 13:35:30,260 - __main__ - INFO - Processing chunk 34: records 165000-170000
2025-07-22 13:35:35,753 - __main__ - INFO - Processing chunk 35: records 170000-175000
2025-07-22 13:35:41,159 - __main__ - INFO - Processing chunk 36: records 175000-180000
2025-07-22 13:35:47,112 - __main__ - INFO - Progress: 180000/393297 records processed (45.8%)
2025-07-22 13:35:47,112 - __main__ - INFO - Processing chunk 37: records 180000-185000
2025-07-22 13:35:52,809 - __main__ - INFO - Processing chunk 38: records 185000-190000
2025-07-22 13:35:58,957 - __main__ - INFO - Processing chunk 39: records 190000-195000
2025-07-22 13:36:05,376 - __main__ - INFO - Processing chunk 40: records 195000-200000
2025-07-22 13:36:11,799 - __main__ - INFO - Processing chunk 41: records 200000-205000
2025-07-22 13:36:19,178 - __main__ - INFO - Progress: 205000/393297 records processed (52.1%)
2025-07-22 13:36:19,178 - __main__ - INFO - Processing chunk 42: records 205000-210000
2025-07-22 13:36:26,538 - __main__ - INFO - Processing chunk 43: records 210000-215000
2025-07-22 13:36:32,356 - __main__ - INFO - Processing chunk 44: records 215000-220000
2025-07-22 13:36:38,299 - __main__ - INFO - Processing chunk 45: records 220000-225000
2025-07-22 13:36:44,151 - __main__ - INFO - Processing chunk 46: records 225000-230000
2025-07-22 13:36:50,429 - __main__ - INFO - Progress: 230000/393297 records processed (58.5%)
2025-07-22 13:36:50,429 - __main__ - INFO - Processing chunk 47: records 230000-235000
2025-07-22 13:36:56,408 - __main__ - INFO - Processing chunk 48: records 235000-240000
2025-07-22 13:37:02,316 - __main__ - INFO - Processing chunk 49: records 240000-245000
2025-07-22 13:37:08,366 - __main__ - INFO - Processing chunk 50: records 245000-250000
2025-07-22 13:37:15,821 - __main__ - INFO - Processing chunk 51: records 250000-255000
2025-07-22 13:37:22,825 - __main__ - INFO - Progress: 255000/393297 records processed (64.8%)
2025-07-22 13:37:22,826 - __main__ - INFO - Processing chunk 52: records 255000-260000
2025-07-22 13:37:28,905 - __main__ - INFO - Processing chunk 53: records 260000-265000
2025-07-22 13:37:35,730 - __main__ - INFO - Processing chunk 54: records 265000-270000
2025-07-22 13:37:42,075 - __main__ - INFO - Processing chunk 55: records 270000-275000
2025-07-22 13:37:48,114 - __main__ - INFO - Processing chunk 56: records 275000-280000
2025-07-22 13:37:54,334 - __main__ - INFO - Progress: 280000/393297 records processed (71.2%)
2025-07-22 13:37:54,334 - __main__ - INFO - Processing chunk 57: records 280000-285000
2025-07-22 13:38:00,845 - __main__ - INFO - Processing chunk 58: records 285000-290000
2025-07-22 13:38:07,091 - __main__ - INFO - Processing chunk 59: records 290000-295000
2025-07-22 13:38:14,035 - __main__ - INFO - Processing chunk 60: records 295000-300000
2025-07-22 13:38:20,766 - __main__ - INFO - Processing chunk 61: records 300000-305000
2025-07-22 13:38:27,228 - __main__ - INFO - Progress: 305000/393297 records processed (77.5%)
2025-07-22 13:38:27,228 - __main__ - INFO - Processing chunk 62: records 305000-310000
2025-07-22 13:38:34,679 - __main__ - INFO - Processing chunk 63: records 310000-315000
2025-07-22 13:38:41,258 - __main__ - INFO - Processing chunk 64: records 315000-320000
2025-07-22 13:38:47,776 - __main__ - INFO - Processing chunk 65: records 320000-325000
2025-07-22 13:38:54,345 - __main__ - INFO - Processing chunk 66: records 325000-330000
2025-07-22 13:39:00,802 - __main__ - INFO - Progress: 330000/393297 records processed (83.9%)
2025-07-22 13:39:00,803 - __main__ - INFO - Processing chunk 67: records 330000-335000
2025-07-22 13:39:07,574 - __main__ - INFO - Processing chunk 68: records 335000-340000
2025-07-22 13:39:14,903 - __main__ - INFO - Processing chunk 69: records 340000-345000
2025-07-22 13:39:22,110 - __main__ - INFO - Processing chunk 70: records 345000-350000
2025-07-22 13:39:29,172 - __main__ - INFO - Processing chunk 71: records 350000-355000
2025-07-22 13:39:41,051 - __main__ - INFO - Progress: 355000/393297 records processed (90.3%)
2025-07-22 13:39:41,074 - __main__ - INFO - Processing chunk 72: records 355000-360000
2025-07-22 13:39:51,531 - __main__ - INFO - Processing chunk 73: records 360000-365000
2025-07-22 13:39:59,039 - __main__ - INFO - Processing chunk 74: records 365000-370000
2025-07-22 13:40:06,467 - __main__ - INFO - Processing chunk 75: records 370000-375000
2025-07-22 13:40:13,793 - __main__ - INFO - Processing chunk 76: records 375000-380000
2025-07-22 13:40:21,523 - __main__ - INFO - Progress: 380000/393297 records processed (96.6%)
2025-07-22 13:40:21,523 - __main__ - INFO - Processing chunk 77: records 380000-385000
2025-07-22 13:40:28,725 - __main__ - INFO - Processing chunk 78: records 385000-390000
2025-07-22 13:40:36,356 - __main__ - INFO - Processing chunk 79: records 390000-393297
2025-07-22 13:40:42,177 - __main__ - INFO - Saving BM25 index to disk: 659436 terms, 393297 documents
2025-07-22 13:40:47,172 - __main__ - INFO - BM25 index saved to disk
2025-07-22 13:40:47,397 - __main__ - INFO - GC: 0 objects collected, 9.7MB freed
2025-07-22 13:40:47,568 - __main__ - INFO - Data loading complete: 393297 records saved to search_cache
2025-07-22 13:40:47,711 - __main__ - INFO - Chunked cache loading completed in 497.49s
2025-07-22 13:40:47,712 - __main__ - INFO - Loaded 393297 records with memory-mapped storage
2025-07-22 13:40:47,996 - __main__ - INFO - Search cache loaded: 393297 records
2025-07-22 13:40:47,997 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 13:40:47,997 - __main__ - INFO - Starting system monitor...
2025-07-22 13:40:47,999 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 13:40:48,000 - __main__ - INFO - Starting cache update monitor...
2025-07-22 13:40:48,001 - __main__ - INFO - Cache update monitor started
2025-07-22 13:40:48,001 - __main__ - INFO - Cache update monitor started
2025-07-22 13:40:48,057 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 13:40:48,059 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 13:40:48,059 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 13:40:48,066 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 13:40:48,066 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 13:40:48,276 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 13:40:48,276 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 13:40:48,277 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 13:40:48,325 - __main__ - INFO - Extending embeddings from 393297 to 393298 records
2025-07-22 13:40:48,501 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 13:40:49,849 - __main__ - INFO - Embeddings file extended successfully to 393298 records
2025-07-22 13:40:49,850 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 13:40:49,856 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393298 documents
2025-07-22 13:40:54,876 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 13:40:55,227 - __main__ - INFO - Incremental cache update completed in 6.95s
2025-07-22 13:40:55,228 - __main__ - INFO - Cache now contains 393298 total records
2025-07-22 13:40:55,428 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393298, 'update_time_seconds': 6.950061798095703}
2025-07-22 13:40:55,429 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393298, 'update_time_seconds': 6.950061798095703, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 13:41:55,434 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 13:41:55,436 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 13:41:55,436 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 13:41:55,444 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 13:41:55,444 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 13:41:55,638 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 13:41:55,639 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 13:41:55,639 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 13:41:55,690 - __main__ - INFO - Extending embeddings from 393298 to 393299 records
2025-07-22 13:41:55,852 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 13:41:57,158 - __main__ - INFO - Embeddings file extended successfully to 393299 records
2025-07-22 13:41:57,159 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 13:41:57,164 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393299 documents
2025-07-22 13:42:03,010 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 13:42:03,322 - __main__ - INFO - Incremental cache update completed in 7.68s
2025-07-22 13:42:03,322 - __main__ - INFO - Cache now contains 393299 total records
2025-07-22 13:42:03,541 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393299, 'update_time_seconds': 7.6831583976745605}
2025-07-22 13:42:03,542 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393299, 'update_time_seconds': 7.6831583976745605, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 13:42:05,065 - __main__ - INFO - Shutting down Search API...
2025-07-22 13:42:07,067 - __main__ - INFO - Cache update monitor stopped
2025-07-22 13:42:08,049 - __main__ - INFO - System monitor stopped
2025-07-22 13:42:08,052 - __main__ - INFO - Database connection closed
2025-07-22 14:06:30,907 - __main__ - INFO - Starting up Search API...
2025-07-22 14:06:30,907 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:06:30,911 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:06:30,911 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:06:33,772 - __main__ - INFO - Model loaded successfully
2025-07-22 14:06:33,772 - __main__ - INFO - Initializing database connection...
2025-07-22 14:06:33,879 - __main__ - INFO - Database connection established
2025-07-22 14:06:33,879 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:06:33,880 - __main__ - INFO - All cache files found on disk
2025-07-22 14:06:33,881 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:06:33,918 - __main__ - INFO - Loading 393299 records from existing cache (576.1MB)
2025-07-22 14:06:33,949 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:06:36,504 - __main__ - INFO - BM25 index loaded: 659436 terms, 393299 documents
2025-07-22 14:06:36,505 - __main__ - INFO - Cache loaded from disk in 2.62s
2025-07-22 14:06:36,505 - __main__ - INFO - Loaded 393299 records with memory-mapped storage
2025-07-22 14:06:36,946 - __main__ - INFO - Search cache loaded: 393299 records
2025-07-22 14:06:36,946 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:06:36,947 - __main__ - INFO - Starting system monitor...
2025-07-22 14:06:36,948 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:06:36,949 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:06:36,950 - __main__ - INFO - Cache update monitor started
2025-07-22 14:06:36,950 - __main__ - INFO - Cache update monitor started
2025-07-22 14:06:37,023 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.217000'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:06:58,136 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:07:00,145 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:07:01,148 - __main__ - INFO - System monitor stopped
2025-07-22 14:07:01,149 - __main__ - INFO - Database connection closed
2025-07-22 14:08:40,748 - __main__ - INFO - Starting up Search API...
2025-07-22 14:08:40,749 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:08:40,752 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:08:40,752 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:08:43,442 - __main__ - INFO - Model loaded successfully
2025-07-22 14:08:43,442 - __main__ - INFO - Initializing database connection...
2025-07-22 14:08:43,614 - __main__ - INFO - Database connection established
2025-07-22 14:08:43,615 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:08:43,616 - __main__ - INFO - All cache files found on disk
2025-07-22 14:08:43,616 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:08:43,656 - __main__ - INFO - Loading 393299 records from existing cache (576.1MB)
2025-07-22 14:08:43,692 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:08:46,418 - __main__ - INFO - BM25 index loaded: 659436 terms, 393299 documents
2025-07-22 14:08:46,419 - __main__ - INFO - Cache loaded from disk in 2.80s
2025-07-22 14:08:46,419 - __main__ - INFO - Loaded 393299 records with memory-mapped storage
2025-07-22 14:08:46,804 - __main__ - INFO - Search cache loaded: 393299 records
2025-07-22 14:08:46,804 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:08:46,805 - __main__ - INFO - Starting system monitor...
2025-07-22 14:08:46,806 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:08:46,807 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:08:46,808 - __main__ - INFO - Cache update monitor started
2025-07-22 14:08:46,808 - __main__ - INFO - Cache update monitor started
2025-07-22 14:10:05,521 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:10:07,530 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:10:08,534 - __main__ - INFO - System monitor stopped
2025-07-22 14:10:08,536 - __main__ - INFO - Database connection closed
2025-07-22 14:11:06,621 - __main__ - INFO - Starting up Search API...
2025-07-22 14:11:06,622 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:11:06,629 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:11:06,629 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:11:10,641 - __main__ - INFO - Model loaded successfully
2025-07-22 14:11:10,642 - __main__ - INFO - Initializing database connection...
2025-07-22 14:11:10,791 - __main__ - INFO - Database connection established
2025-07-22 14:11:10,792 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:11:10,793 - __main__ - INFO - All cache files found on disk
2025-07-22 14:11:10,794 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:11:10,857 - __main__ - INFO - Loading 393299 records from existing cache (576.1MB)
2025-07-22 14:11:10,926 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:11:14,232 - __main__ - INFO - BM25 index loaded: 659436 terms, 393299 documents
2025-07-22 14:11:14,233 - __main__ - INFO - Cache loaded from disk in 3.44s
2025-07-22 14:11:14,233 - __main__ - INFO - Loaded 393299 records with memory-mapped storage
2025-07-22 14:11:14,785 - __main__ - INFO - Search cache loaded: 393299 records
2025-07-22 14:11:14,786 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:11:14,786 - __main__ - INFO - Starting system monitor...
2025-07-22 14:11:14,788 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:11:14,789 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:11:14,790 - __main__ - INFO - Cache update monitor started
2025-07-22 14:11:14,790 - __main__ - INFO - Cache update monitor started
2025-07-22 14:12:22,195 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:12:24,196 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:12:24,906 - __main__ - INFO - System monitor stopped
2025-07-22 14:12:24,914 - __main__ - INFO - Database connection closed
2025-07-22 14:16:16,134 - __main__ - INFO - Starting up Search API...
2025-07-22 14:16:16,134 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:16:16,138 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:16:16,138 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:16:18,992 - __main__ - INFO - Model loaded successfully
2025-07-22 14:16:18,993 - __main__ - INFO - Initializing database connection...
2025-07-22 14:16:19,138 - __main__ - INFO - Database connection established
2025-07-22 14:16:19,139 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:16:19,140 - __main__ - INFO - All cache files found on disk
2025-07-22 14:16:19,140 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:16:19,189 - __main__ - INFO - Loading 393299 records from existing cache (576.1MB)
2025-07-22 14:16:19,238 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:16:21,835 - __main__ - INFO - BM25 index loaded: 659436 terms, 393299 documents
2025-07-22 14:16:21,836 - __main__ - INFO - Cache loaded from disk in 2.70s
2025-07-22 14:16:21,836 - __main__ - INFO - Loaded 393299 records with memory-mapped storage
2025-07-22 14:16:22,319 - __main__ - INFO - Search cache loaded: 393299 records
2025-07-22 14:16:22,320 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:16:22,320 - __main__ - INFO - Starting system monitor...
2025-07-22 14:16:22,322 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:16:22,323 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:16:22,324 - __main__ - INFO - Cache update monitor started
2025-07-22 14:16:22,324 - __main__ - INFO - Cache update monitor started
2025-07-22 14:16:22,330 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:16:22,333 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:16:22,334 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:16:22,341 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:16:22,342 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:16:22,596 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:16:22,597 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:16:22,598 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:16:22,634 - __main__ - INFO - Extending embeddings from 393299 to 393300 records
2025-07-22 14:16:22,800 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:16:24,056 - __main__ - INFO - Embeddings file extended successfully to 393300 records
2025-07-22 14:16:24,057 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:16:24,061 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393300 documents
2025-07-22 14:16:29,045 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:16:29,353 - __main__ - INFO - Incremental cache update completed in 6.75s
2025-07-22 14:16:29,353 - __main__ - INFO - Cache now contains 393300 total records
2025-07-22 14:16:29,595 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393300, 'update_time_seconds': 6.75398588180542}
2025-07-22 14:16:29,596 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393300, 'update_time_seconds': 6.75398588180542, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:17:29,602 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:17:29,605 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:17:29,605 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:17:29,615 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:17:29,616 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:17:29,861 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:17:29,862 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:17:29,863 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:17:29,914 - __main__ - INFO - Extending embeddings from 393300 to 393301 records
2025-07-22 14:17:30,148 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:17:31,709 - __main__ - INFO - Embeddings file extended successfully to 393301 records
2025-07-22 14:17:31,709 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:17:31,715 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393301 documents
2025-07-22 14:17:37,938 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:17:38,314 - __main__ - INFO - Incremental cache update completed in 8.45s
2025-07-22 14:17:38,315 - __main__ - INFO - Cache now contains 393301 total records
2025-07-22 14:17:38,550 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393301, 'update_time_seconds': 8.451345682144165}
2025-07-22 14:17:38,551 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393301, 'update_time_seconds': 8.451345682144165, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:18:38,556 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:18:38,558 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:18:38,558 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:18:38,564 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:18:38,564 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:18:38,743 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:18:38,744 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:18:38,744 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:18:38,795 - __main__ - INFO - Extending embeddings from 393301 to 393302 records
2025-07-22 14:18:38,969 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:18:40,178 - __main__ - INFO - Embeddings file extended successfully to 393302 records
2025-07-22 14:18:40,178 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:18:40,183 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393302 documents
2025-07-22 14:18:45,602 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:18:45,875 - __main__ - INFO - Incremental cache update completed in 7.13s
2025-07-22 14:18:45,876 - __main__ - INFO - Cache now contains 393302 total records
2025-07-22 14:18:46,061 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393302, 'update_time_seconds': 7.131343603134155}
2025-07-22 14:18:46,062 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393302, 'update_time_seconds': 7.131343603134155, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:19:16,257 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:19:18,271 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:19:19,277 - __main__ - INFO - System monitor stopped
2025-07-22 14:19:19,285 - __main__ - INFO - Database connection closed
2025-07-22 14:28:46,905 - __main__ - INFO - Starting up Search API...
2025-07-22 14:28:46,905 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:28:46,909 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:28:46,909 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:28:49,759 - __main__ - INFO - Model loaded successfully
2025-07-22 14:28:49,759 - __main__ - INFO - Initializing database connection...
2025-07-22 14:28:49,890 - __main__ - INFO - Database connection established
2025-07-22 14:28:49,890 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:28:49,892 - __main__ - INFO - All cache files found on disk
2025-07-22 14:28:49,892 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:28:49,950 - __main__ - INFO - Loading 393302 records from existing cache (576.1MB)
2025-07-22 14:28:50,014 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:28:52,897 - __main__ - INFO - BM25 index loaded: 659436 terms, 393302 documents
2025-07-22 14:28:52,897 - __main__ - INFO - Cache loaded from disk in 3.01s
2025-07-22 14:28:52,898 - __main__ - INFO - Loaded 393302 records with memory-mapped storage
2025-07-22 14:28:53,431 - __main__ - INFO - Search cache loaded: 393302 records
2025-07-22 14:28:53,431 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:28:53,431 - __main__ - INFO - Starting system monitor...
2025-07-22 14:28:53,433 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:28:53,434 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:28:53,435 - __main__ - INFO - Cache update monitor started
2025-07-22 14:28:53,435 - __main__ - INFO - Cache update monitor started
2025-07-22 14:28:53,462 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:28:53,478 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:28:53,479 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:28:53,494 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:28:53,495 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:28:53,713 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:28:53,714 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:28:53,715 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:28:53,759 - __main__ - INFO - Extending embeddings from 393302 to 393303 records
2025-07-22 14:28:53,957 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:28:55,290 - __main__ - INFO - Embeddings file extended successfully to 393303 records
2025-07-22 14:28:55,290 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:28:55,296 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393303 documents
2025-07-22 14:29:00,516 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:29:02,723 - __main__ - INFO - Incremental cache update completed in 9.01s
2025-07-22 14:29:02,724 - __main__ - INFO - Cache now contains 393303 total records
2025-07-22 14:29:02,969 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393303, 'update_time_seconds': 9.009053945541382}
2025-07-22 14:29:02,970 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393303, 'update_time_seconds': 9.009053945541382, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:30:02,979 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:31:02,986 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:31:10,648 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:31:12,651 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:31:13,458 - __main__ - INFO - System monitor stopped
2025-07-22 14:31:13,459 - __main__ - INFO - Database connection closed
2025-07-22 14:33:40,397 - __main__ - INFO - Starting up Search API...
2025-07-22 14:33:40,398 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:33:40,401 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:33:40,401 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:33:43,112 - __main__ - INFO - Model loaded successfully
2025-07-22 14:33:43,113 - __main__ - INFO - Initializing database connection...
2025-07-22 14:33:43,234 - __main__ - INFO - Database connection established
2025-07-22 14:33:43,235 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:33:43,236 - __main__ - INFO - All cache files found on disk
2025-07-22 14:33:43,237 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:33:43,291 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:33:43,338 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:33:45,983 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:33:45,983 - __main__ - INFO - Cache loaded from disk in 2.75s
2025-07-22 14:33:45,984 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:33:46,391 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:33:46,392 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:33:46,392 - __main__ - INFO - Starting system monitor...
2025-07-22 14:33:46,395 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:33:46,395 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:33:46,396 - __main__ - INFO - Cache update monitor started
2025-07-22 14:33:46,397 - __main__ - INFO - Cache update monitor started
2025-07-22 14:36:01,415 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:36:03,430 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:36:04,437 - __main__ - INFO - System monitor stopped
2025-07-22 14:36:04,450 - __main__ - INFO - Database connection closed
2025-07-22 14:36:23,992 - __main__ - INFO - Starting up Search API...
2025-07-22 14:36:23,992 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:36:23,996 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:36:23,996 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:36:26,485 - __main__ - INFO - Model loaded successfully
2025-07-22 14:36:26,485 - __main__ - INFO - Initializing database connection...
2025-07-22 14:36:26,723 - __main__ - INFO - Database connection established
2025-07-22 14:36:26,723 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:36:26,724 - __main__ - INFO - All cache files found on disk
2025-07-22 14:36:26,724 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:36:26,761 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:36:26,793 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:36:29,295 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:36:29,296 - __main__ - INFO - Cache loaded from disk in 2.57s
2025-07-22 14:36:29,296 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:36:29,739 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:36:29,740 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:36:29,740 - __main__ - INFO - Starting system monitor...
2025-07-22 14:36:29,741 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:36:29,742 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:36:29,742 - __main__ - INFO - Cache update monitor started
2025-07-22 14:36:29,742 - __main__ - INFO - Cache update monitor started
2025-07-22 14:38:02,877 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:38:04,888 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:38:05,902 - __main__ - INFO - System monitor stopped
2025-07-22 14:38:05,905 - __main__ - INFO - Database connection closed
2025-07-22 14:41:36,140 - __main__ - INFO - Starting up Search API...
2025-07-22 14:41:36,140 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:41:36,144 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:41:36,144 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:41:38,912 - __main__ - INFO - Model loaded successfully
2025-07-22 14:41:38,913 - __main__ - INFO - Initializing database connection...
2025-07-22 14:41:39,017 - __main__ - INFO - Database connection established
2025-07-22 14:41:39,017 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:41:39,018 - __main__ - INFO - All cache files found on disk
2025-07-22 14:41:39,019 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:41:39,067 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:41:39,108 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:41:41,679 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:41:41,680 - __main__ - INFO - Cache loaded from disk in 2.66s
2025-07-22 14:41:41,680 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:41:42,082 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:41:42,083 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:41:42,083 - __main__ - INFO - Starting system monitor...
2025-07-22 14:41:42,085 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:41:42,086 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:41:42,086 - __main__ - INFO - Cache update monitor started
2025-07-22 14:41:42,086 - __main__ - INFO - Cache update monitor started
2025-07-22 14:41:42,087 - __main__ - INFO - str: 2016-05-31 16:04:14.
2025-07-22 14:41:42,098 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:42:08,036 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:42:10,052 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:42:11,058 - __main__ - INFO - System monitor stopped
2025-07-22 14:42:11,059 - __main__ - INFO - Database connection closed
2025-07-22 14:42:28,369 - __main__ - INFO - Starting up Search API...
2025-07-22 14:42:28,369 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:42:28,373 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:42:28,373 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:42:30,843 - __main__ - INFO - Model loaded successfully
2025-07-22 14:42:30,843 - __main__ - INFO - Initializing database connection...
2025-07-22 14:42:30,962 - __main__ - INFO - Database connection established
2025-07-22 14:42:30,962 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:42:30,964 - __main__ - INFO - All cache files found on disk
2025-07-22 14:42:30,964 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:42:31,003 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:42:31,041 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:42:33,696 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:42:33,696 - __main__ - INFO - Cache loaded from disk in 2.73s
2025-07-22 14:42:33,697 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:42:34,096 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:42:34,097 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:42:34,097 - __main__ - INFO - Starting system monitor...
2025-07-22 14:42:34,099 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:42:34,100 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:42:34,101 - __main__ - INFO - Cache update monitor started
2025-07-22 14:42:34,101 - __main__ - INFO - Cache update monitor started
2025-07-22 14:42:34,101 - __main__ - INFO - str: 2016-05-31 16:04:14.
2025-07-22 14:42:34,109 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:42:47,748 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:42:49,750 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:42:50,756 - __main__ - INFO - System monitor stopped
2025-07-22 14:42:50,757 - __main__ - INFO - Database connection closed
2025-07-22 14:43:09,223 - __main__ - INFO - Starting up Search API...
2025-07-22 14:43:09,224 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:43:09,232 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:43:09,233 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:43:12,267 - __main__ - INFO - Model loaded successfully
2025-07-22 14:43:12,267 - __main__ - INFO - Initializing database connection...
2025-07-22 14:43:12,362 - __main__ - INFO - Database connection established
2025-07-22 14:43:12,362 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:43:12,363 - __main__ - INFO - All cache files found on disk
2025-07-22 14:43:12,364 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:43:12,397 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:43:12,430 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:43:15,087 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:43:15,088 - __main__ - INFO - Cache loaded from disk in 2.73s
2025-07-22 14:43:15,088 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:43:15,498 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:43:15,499 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:43:15,499 - __main__ - INFO - Starting system monitor...
2025-07-22 14:43:15,501 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:43:15,502 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:43:15,503 - __main__ - INFO - Cache update monitor started
2025-07-22 14:43:15,503 - __main__ - INFO - Cache update monitor started
2025-07-22 14:43:15,512 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:44:03,535 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:44:05,539 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:44:06,555 - __main__ - INFO - System monitor stopped
2025-07-22 14:44:06,556 - __main__ - INFO - Database connection closed
2025-07-22 14:44:25,169 - __main__ - INFO - Starting up Search API...
2025-07-22 14:44:25,169 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:44:25,173 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:44:25,173 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:44:27,641 - __main__ - INFO - Model loaded successfully
2025-07-22 14:44:27,642 - __main__ - INFO - Initializing database connection...
2025-07-22 14:44:27,756 - __main__ - INFO - Database connection established
2025-07-22 14:44:27,757 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:44:27,758 - __main__ - INFO - All cache files found on disk
2025-07-22 14:44:27,758 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:44:27,797 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:44:27,829 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:44:30,411 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:44:30,412 - __main__ - INFO - Cache loaded from disk in 2.65s
2025-07-22 14:44:30,412 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:44:30,874 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:44:30,875 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:44:30,875 - __main__ - INFO - Starting system monitor...
2025-07-22 14:44:30,877 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:44:30,877 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:44:30,878 - __main__ - INFO - Cache update monitor started
2025-07-22 14:44:30,878 - __main__ - INFO - Cache update monitor started
2025-07-22 14:45:22,124 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:45:24,127 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:45:25,135 - __main__ - INFO - System monitor stopped
2025-07-22 14:45:25,138 - __main__ - INFO - Database connection closed
2025-07-22 14:45:42,204 - __main__ - INFO - Starting up Search API...
2025-07-22 14:45:42,204 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:45:42,208 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:45:42,208 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:45:44,961 - __main__ - INFO - Model loaded successfully
2025-07-22 14:45:44,961 - __main__ - INFO - Initializing database connection...
2025-07-22 14:45:45,111 - __main__ - INFO - Database connection established
2025-07-22 14:45:45,112 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:45:45,114 - __main__ - INFO - All cache files found on disk
2025-07-22 14:45:45,114 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:45:45,158 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:45:45,194 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:45:47,808 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:45:47,809 - __main__ - INFO - Cache loaded from disk in 2.70s
2025-07-22 14:45:47,809 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:45:48,266 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:45:48,267 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:45:48,267 - __main__ - INFO - Starting system monitor...
2025-07-22 14:45:48,269 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:45:48,270 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:45:48,270 - __main__ - INFO - Cache update monitor started
2025-07-22 14:45:48,270 - __main__ - INFO - Cache update monitor started
2025-07-22 14:46:45,282 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:46:47,284 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:46:48,277 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:46:48,281 - __main__ - INFO - System monitor stopped
2025-07-22 14:46:48,291 - __main__ - INFO - Database connection closed
2025-07-22 14:47:30,195 - __main__ - INFO - Starting up Search API...
2025-07-22 14:47:30,195 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:47:30,198 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:47:30,198 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:47:33,346 - __main__ - INFO - Model loaded successfully
2025-07-22 14:47:33,347 - __main__ - INFO - Initializing database connection...
2025-07-22 14:47:33,447 - __main__ - INFO - Database connection established
2025-07-22 14:47:33,447 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:47:33,448 - __main__ - INFO - All cache files found on disk
2025-07-22 14:47:33,449 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:47:33,486 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:47:33,519 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:47:36,181 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:47:36,182 - __main__ - INFO - Cache loaded from disk in 2.73s
2025-07-22 14:47:36,182 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:47:36,688 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:47:36,688 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:47:36,689 - __main__ - INFO - Starting system monitor...
2025-07-22 14:47:36,691 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:47:36,691 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:47:36,692 - __main__ - INFO - Cache update monitor started
2025-07-22 14:47:36,692 - __main__ - INFO - Cache update monitor started
2025-07-22 14:47:36,699 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:48:36,720 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:49:36,727 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 14:49:44,476 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:49:46,487 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:49:46,712 - __main__ - INFO - System monitor stopped
2025-07-22 14:49:46,713 - __main__ - INFO - Database connection closed
2025-07-22 14:50:04,806 - __main__ - INFO - Starting up Search API...
2025-07-22 14:50:04,806 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:50:04,810 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:50:04,810 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:50:07,638 - __main__ - INFO - Model loaded successfully
2025-07-22 14:50:07,639 - __main__ - INFO - Initializing database connection...
2025-07-22 14:50:07,766 - __main__ - INFO - Database connection established
2025-07-22 14:50:07,766 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:50:07,767 - __main__ - INFO - All cache files found on disk
2025-07-22 14:50:07,768 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:50:07,817 - __main__ - INFO - Loading 393303 records from existing cache (576.1MB)
2025-07-22 14:50:07,862 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:50:10,574 - __main__ - INFO - BM25 index loaded: 659436 terms, 393303 documents
2025-07-22 14:50:10,575 - __main__ - INFO - Cache loaded from disk in 2.81s
2025-07-22 14:50:10,575 - __main__ - INFO - Loaded 393303 records with memory-mapped storage
2025-07-22 14:50:10,992 - __main__ - INFO - Search cache loaded: 393303 records
2025-07-22 14:50:10,993 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:50:10,993 - __main__ - INFO - Starting system monitor...
2025-07-22 14:50:10,996 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:50:10,996 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:50:10,998 - __main__ - INFO - Cache update monitor started
2025-07-22 14:50:10,998 - __main__ - INFO - Cache update monitor started
2025-07-22 14:50:11,004 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217
2025-07-22 14:50:11,007 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:50:11,007 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217 with chunk size 5000
2025-07-22 14:50:11,015 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:50:11,015 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:50:11,225 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:50:11,226 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:50:11,226 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:50:11,265 - __main__ - INFO - Extending embeddings from 393303 to 393304 records
2025-07-22 14:50:11,484 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:50:12,939 - __main__ - INFO - Embeddings file extended successfully to 393304 records
2025-07-22 14:50:12,940 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:50:12,945 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393304 documents
2025-07-22 14:50:17,830 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:50:18,111 - __main__ - INFO - Incremental cache update completed in 6.88s
2025-07-22 14:50:18,112 - __main__ - INFO - Cache now contains 393304 total records
2025-07-22 14:50:18,292 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393304, 'update_time_seconds': 6.884415864944458}
2025-07-22 14:50:18,293 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393304, 'update_time_seconds': 6.884415864944458, 'latest_entered_time': '2016-05-31 16:04:14.217', 'memory_used_mb': 0.00146484375}
2025-07-22 14:51:18,297 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:51:18,300 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:51:18,300 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:51:18,307 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:51:18,308 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:51:18,604 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:51:18,605 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:51:18,605 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:51:18,670 - __main__ - INFO - Extending embeddings from 393304 to 393305 records
2025-07-22 14:51:18,897 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:51:20,837 - __main__ - INFO - Embeddings file extended successfully to 393305 records
2025-07-22 14:51:20,837 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:51:20,844 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393305 documents
2025-07-22 14:51:29,142 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:51:29,589 - __main__ - INFO - Incremental cache update completed in 10.98s
2025-07-22 14:51:29,590 - __main__ - INFO - Cache now contains 393305 total records
2025-07-22 14:51:29,873 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393305, 'update_time_seconds': 10.984071254730225}
2025-07-22 14:51:29,874 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393305, 'update_time_seconds': 10.984071254730225, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:51:38,309 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:51:40,320 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:51:41,063 - __main__ - INFO - System monitor stopped
2025-07-22 14:51:41,071 - __main__ - INFO - Database connection closed
2025-07-22 14:52:06,928 - __main__ - INFO - Starting up Search API...
2025-07-22 14:52:06,929 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 14:52:06,932 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 14:52:06,932 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 14:52:09,749 - __main__ - INFO - Model loaded successfully
2025-07-22 14:52:09,750 - __main__ - INFO - Initializing database connection...
2025-07-22 14:52:09,870 - __main__ - INFO - Database connection established
2025-07-22 14:52:09,871 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 14:52:09,872 - __main__ - INFO - All cache files found on disk
2025-07-22 14:52:09,873 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 14:52:09,932 - __main__ - INFO - Loading 393305 records from existing cache (576.1MB)
2025-07-22 14:52:09,990 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 14:52:12,763 - __main__ - INFO - BM25 index loaded: 659436 terms, 393305 documents
2025-07-22 14:52:12,764 - __main__ - INFO - Cache loaded from disk in 2.89s
2025-07-22 14:52:12,764 - __main__ - INFO - Loaded 393305 records with memory-mapped storage
2025-07-22 14:52:13,164 - __main__ - INFO - Search cache loaded: 393305 records
2025-07-22 14:52:13,164 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 14:52:13,165 - __main__ - INFO - Starting system monitor...
2025-07-22 14:52:13,166 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 14:52:13,167 - __main__ - INFO - Starting cache update monitor...
2025-07-22 14:52:13,168 - __main__ - INFO - Cache update monitor started
2025-07-22 14:52:13,168 - __main__ - INFO - Cache update monitor started
2025-07-22 14:52:13,173 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:52:13,176 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:52:13,176 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:52:13,183 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:52:13,183 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:52:13,362 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:52:13,362 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:52:13,363 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:52:13,396 - __main__ - INFO - Extending embeddings from 393305 to 393306 records
2025-07-22 14:52:13,620 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:52:14,991 - __main__ - INFO - Embeddings file extended successfully to 393306 records
2025-07-22 14:52:14,992 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:52:14,997 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393306 documents
2025-07-22 14:52:20,240 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:52:20,500 - __main__ - INFO - Incremental cache update completed in 7.14s
2025-07-22 14:52:20,501 - __main__ - INFO - Cache now contains 393306 total records
2025-07-22 14:52:20,716 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393306, 'update_time_seconds': 7.137373685836792}
2025-07-22 14:52:20,716 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393306, 'update_time_seconds': 7.137373685836792, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:53:20,721 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:53:20,723 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:53:20,724 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:53:20,730 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:53:20,730 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:53:20,995 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:53:20,996 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:53:20,997 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:53:21,050 - __main__ - INFO - Extending embeddings from 393306 to 393307 records
2025-07-22 14:53:21,295 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:53:22,691 - __main__ - INFO - Embeddings file extended successfully to 393307 records
2025-07-22 14:53:22,691 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:53:22,696 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393307 documents
2025-07-22 14:53:28,181 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:53:28,528 - __main__ - INFO - Incremental cache update completed in 7.53s
2025-07-22 14:53:28,529 - __main__ - INFO - Cache now contains 393307 total records
2025-07-22 14:53:28,806 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393307, 'update_time_seconds': 7.531312465667725}
2025-07-22 14:53:28,807 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393307, 'update_time_seconds': 7.531312465667725, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:54:28,812 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:54:28,814 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:54:28,815 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:54:28,850 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:54:28,851 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:54:29,166 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:54:29,167 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:54:29,168 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:54:29,237 - __main__ - INFO - Extending embeddings from 393307 to 393308 records
2025-07-22 14:54:29,493 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:54:31,004 - __main__ - INFO - Embeddings file extended successfully to 393308 records
2025-07-22 14:54:31,004 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:54:31,009 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393308 documents
2025-07-22 14:54:37,344 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:54:37,860 - __main__ - INFO - Incremental cache update completed in 8.69s
2025-07-22 14:54:37,861 - __main__ - INFO - Cache now contains 393308 total records
2025-07-22 14:54:38,216 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393308, 'update_time_seconds': 8.69261360168457}
2025-07-22 14:54:38,217 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393308, 'update_time_seconds': 8.69261360168457, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:55:38,221 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:55:38,223 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:55:38,224 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:55:38,231 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:55:38,231 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:55:38,492 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:55:38,493 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:55:38,493 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:55:38,544 - __main__ - INFO - Extending embeddings from 393308 to 393309 records
2025-07-22 14:55:38,773 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:55:40,237 - __main__ - INFO - Embeddings file extended successfully to 393309 records
2025-07-22 14:55:40,237 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:55:40,242 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393309 documents
2025-07-22 14:55:45,973 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:55:46,287 - __main__ - INFO - Incremental cache update completed in 7.79s
2025-07-22 14:55:46,288 - __main__ - INFO - Cache now contains 393309 total records
2025-07-22 14:55:46,556 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393309, 'update_time_seconds': 7.794232368469238}
2025-07-22 14:55:46,557 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393309, 'update_time_seconds': 7.794232368469238, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:56:46,561 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 14:56:46,565 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 14:56:46,566 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 14:56:46,572 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 14:56:46,572 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 14:56:46,835 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 14:56:46,836 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 14:56:46,837 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 14:56:46,891 - __main__ - INFO - Extending embeddings from 393309 to 393310 records
2025-07-22 14:56:47,122 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 14:56:48,529 - __main__ - INFO - Embeddings file extended successfully to 393310 records
2025-07-22 14:56:48,530 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 14:56:48,535 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393310 documents
2025-07-22 14:57:02,634 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 14:57:03,186 - __main__ - INFO - Incremental cache update completed in 16.35s
2025-07-22 14:57:03,187 - __main__ - INFO - Cache now contains 393310 total records
2025-07-22 14:57:03,564 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393310, 'update_time_seconds': 16.348775148391724}
2025-07-22 14:57:03,565 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393310, 'update_time_seconds': 16.348775148391724, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 14:57:24,349 - __main__ - INFO - Shutting down Search API...
2025-07-22 14:57:26,354 - __main__ - INFO - Cache update monitor stopped
2025-07-22 14:57:27,357 - __main__ - INFO - System monitor stopped
2025-07-22 14:57:27,359 - __main__ - INFO - Database connection closed
2025-07-22 15:00:32,387 - __main__ - INFO - Starting up Search API...
2025-07-22 15:00:32,388 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 15:00:32,392 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 15:00:32,392 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 15:00:34,897 - __main__ - INFO - Model loaded successfully
2025-07-22 15:00:34,898 - __main__ - INFO - Initializing database connection...
2025-07-22 15:00:35,021 - __main__ - INFO - Database connection established
2025-07-22 15:00:35,021 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 15:00:35,022 - __main__ - INFO - All cache files found on disk
2025-07-22 15:00:35,022 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 15:00:35,073 - __main__ - INFO - Loading 393310 records from existing cache (576.1MB)
2025-07-22 15:00:35,116 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 15:00:37,775 - __main__ - INFO - BM25 index loaded: 659436 terms, 393310 documents
2025-07-22 15:00:37,775 - __main__ - INFO - Cache loaded from disk in 2.75s
2025-07-22 15:00:37,776 - __main__ - INFO - Loaded 393310 records with memory-mapped storage
2025-07-22 15:00:38,149 - __main__ - INFO - Search cache loaded: 393310 records
2025-07-22 15:00:38,150 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 15:00:38,150 - __main__ - INFO - Starting system monitor...
2025-07-22 15:00:38,151 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 15:00:38,152 - __main__ - INFO - Starting cache update monitor...
2025-07-22 15:00:38,152 - __main__ - INFO - Cache update monitor started
2025-07-22 15:00:38,152 - __main__ - INFO - Cache update monitor started
2025-07-22 15:00:38,157 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-22 15:00:38,160 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-22 15:00:38,160 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-22 15:00:38,166 - __main__ - INFO - Loading 1 new records incrementally
2025-07-22 15:00:38,167 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-22 15:00:38,340 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-22 15:00:38,341 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-22 15:00:38,342 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-22 15:00:38,376 - __main__ - INFO - Extending embeddings from 393310 to 393311 records
2025-07-22 15:00:38,588 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-22 15:00:39,955 - __main__ - INFO - Embeddings file extended successfully to 393311 records
2025-07-22 15:00:39,955 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-22 15:00:39,960 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393311 documents
2025-07-22 15:00:45,134 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-22 15:00:45,434 - __main__ - INFO - Incremental cache update completed in 7.09s
2025-07-22 15:00:45,435 - __main__ - INFO - Cache now contains 393311 total records
2025-07-22 15:00:45,626 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393311, 'update_time_seconds': 7.092357397079468}
2025-07-22 15:00:45,626 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393311, 'update_time_seconds': 7.092357397079468, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-22 15:00:49,486 - __main__ - INFO - Shutting down Search API...
2025-07-22 15:00:51,497 - __main__ - INFO - Cache update monitor stopped
2025-07-22 15:00:52,508 - __main__ - INFO - System monitor stopped
2025-07-22 15:00:52,511 - __main__ - INFO - Database connection closed
2025-07-22 15:02:25,133 - __main__ - INFO - Starting up Search API...
2025-07-22 15:02:25,134 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 15:02:25,140 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 15:02:25,141 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 15:02:27,784 - __main__ - INFO - Model loaded successfully
2025-07-22 15:02:27,784 - __main__ - INFO - Initializing database connection...
2025-07-22 15:02:27,961 - __main__ - INFO - Database connection established
2025-07-22 15:02:27,961 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 15:02:27,963 - __main__ - INFO - All cache files found on disk
2025-07-22 15:02:27,963 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 15:02:28,012 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-22 15:02:28,074 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 15:02:30,712 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-22 15:02:30,713 - __main__ - INFO - Cache loaded from disk in 2.75s
2025-07-22 15:02:30,713 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-22 15:02:31,124 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-22 15:02:31,124 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 15:02:31,125 - __main__ - INFO - Starting system monitor...
2025-07-22 15:02:31,126 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 15:02:31,126 - __main__ - INFO - Starting cache update monitor...
2025-07-22 15:02:31,127 - __main__ - INFO - Cache update monitor started
2025-07-22 15:02:31,127 - __main__ - INFO - Cache update monitor started
2025-07-22 15:02:31,132 - __main__ - ERROR - Failed to check for new records: cannot access local variable 'formatted_time' where it is not associated with a value
2025-07-22 15:03:31,136 - __main__ - ERROR - Failed to check for new records: cannot access local variable 'formatted_time' where it is not associated with a value
2025-07-22 15:03:41,214 - __main__ - INFO - Shutting down Search API...
2025-07-22 15:03:43,229 - __main__ - INFO - Cache update monitor stopped
2025-07-22 15:03:44,237 - __main__ - INFO - System monitor stopped
2025-07-22 15:03:44,240 - __main__ - INFO - Database connection closed
2025-07-22 15:05:02,203 - __main__ - INFO - Starting up Search API...
2025-07-22 15:05:02,204 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 15:05:02,209 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 15:05:02,209 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 15:05:06,138 - __main__ - INFO - Model loaded successfully
2025-07-22 15:05:06,138 - __main__ - INFO - Initializing database connection...
2025-07-22 15:05:06,267 - __main__ - INFO - Database connection established
2025-07-22 15:05:06,267 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 15:05:06,268 - __main__ - INFO - All cache files found on disk
2025-07-22 15:05:06,268 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 15:05:06,312 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-22 15:05:06,348 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 15:05:09,146 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-22 15:05:09,146 - __main__ - INFO - Cache loaded from disk in 2.88s
2025-07-22 15:05:09,147 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-22 15:05:09,629 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-22 15:05:09,630 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 15:05:09,630 - __main__ - INFO - Starting system monitor...
2025-07-22 15:05:09,632 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 15:05:09,632 - __main__ - INFO - Starting cache update monitor...
2025-07-22 15:05:09,633 - __main__ - INFO - Cache update monitor started
2025-07-22 15:05:09,634 - __main__ - INFO - Cache update monitor started
2025-07-22 15:05:09,641 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.217000'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 15:06:09,666 - __main__ - ERROR - Failed to check for new records: (pyodbc.DataError) ('22007', '[22007] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Conversion failed when converting date and/or time from character string. (241) (SQLExecDirectW)')
[SQL: 
                SELECT COUNT(*) as new_count
                FROM ReportNLP
                WHERE empty_report = 0
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND Entered_Time > '2016-05-31 16:04:14.217000'
            ]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-22 15:06:33,654 - __main__ - INFO - Shutting down Search API...
2025-07-22 15:06:35,658 - __main__ - INFO - Cache update monitor stopped
2025-07-22 15:06:36,665 - __main__ - INFO - System monitor stopped
2025-07-22 15:06:36,666 - __main__ - INFO - Database connection closed
2025-07-22 15:06:57,972 - __main__ - INFO - Starting up Search API...
2025-07-22 15:06:57,973 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 15:06:57,980 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 15:06:57,980 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 15:07:01,325 - __main__ - INFO - Model loaded successfully
2025-07-22 15:07:01,325 - __main__ - INFO - Initializing database connection...
2025-07-22 15:07:01,507 - __main__ - INFO - Database connection established
2025-07-22 15:07:01,507 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 15:07:01,508 - __main__ - INFO - All cache files found on disk
2025-07-22 15:07:01,509 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 15:07:01,558 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-22 15:07:01,612 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 15:07:04,942 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-22 15:07:04,943 - __main__ - INFO - Cache loaded from disk in 3.43s
2025-07-22 15:07:04,944 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-22 15:07:05,563 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-22 15:07:05,564 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 15:07:05,564 - __main__ - INFO - Starting system monitor...
2025-07-22 15:07:05,567 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 15:07:05,568 - __main__ - INFO - Starting cache update monitor...
2025-07-22 15:07:05,569 - __main__ - INFO - Cache update monitor started
2025-07-22 15:07:05,569 - __main__ - INFO - Cache update monitor started
2025-07-22 15:08:07,056 - __main__ - INFO - Shutting down Search API...
2025-07-22 15:08:09,066 - __main__ - INFO - Cache update monitor stopped
2025-07-22 15:08:10,068 - __main__ - INFO - System monitor stopped
2025-07-22 15:08:10,071 - __main__ - INFO - Database connection closed
2025-07-23 08:31:52,495 - __main__ - INFO - Starting up Search API...
2025-07-23 08:31:52,496 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-23 08:31:52,500 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 08:31:52,501 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 08:31:56,493 - __main__ - INFO - Model loaded successfully
2025-07-23 08:31:56,494 - __main__ - INFO - Initializing database connection...
2025-07-23 08:31:56,703 - __main__ - INFO - Database connection established
2025-07-23 08:31:56,704 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-23 08:31:56,706 - __main__ - INFO - All cache files found on disk
2025-07-23 08:31:56,706 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-23 08:31:56,774 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-23 08:31:56,840 - __main__ - INFO - Loading BM25 index from disk...
2025-07-23 08:31:59,614 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-23 08:31:59,614 - __main__ - INFO - Cache loaded from disk in 2.91s
2025-07-23 08:31:59,616 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-23 08:32:00,011 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-23 08:32:00,012 - __main__ - INFO - Memory-mapped storage: True
2025-07-23 08:32:00,012 - __main__ - INFO - Starting system monitor...
2025-07-23 08:32:00,014 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-23 08:32:00,015 - __main__ - INFO - Starting cache update monitor...
2025-07-23 08:32:00,016 - __main__ - INFO - Cache update monitor started
2025-07-23 08:32:00,017 - __main__ - INFO - Cache update monitor started
2025-07-23 08:32:23,237 - __main__ - INFO - Shutting down Search API...
2025-07-23 08:32:25,241 - __main__ - INFO - Cache update monitor stopped
2025-07-23 08:32:26,248 - __main__ - INFO - System monitor stopped
2025-07-23 08:32:26,252 - __main__ - INFO - Database connection closed
2025-07-23 08:33:33,864 - __main__ - INFO - Starting up Search API...
2025-07-23 08:33:33,865 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-23 08:33:33,869 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 08:33:33,869 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 08:33:36,443 - __main__ - INFO - Model loaded successfully
2025-07-23 08:33:36,444 - __main__ - INFO - Initializing database connection...
2025-07-23 08:33:36,533 - __main__ - INFO - Database connection established
2025-07-23 08:33:36,533 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-23 08:33:36,534 - __main__ - INFO - All cache files found on disk
2025-07-23 08:33:36,534 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-23 08:33:36,566 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-23 08:33:36,600 - __main__ - INFO - Loading BM25 index from disk...
2025-07-23 08:33:39,251 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-23 08:33:39,251 - __main__ - INFO - Cache loaded from disk in 2.72s
2025-07-23 08:33:39,252 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-23 08:33:39,664 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-23 08:33:39,665 - __main__ - INFO - Memory-mapped storage: True
2025-07-23 08:33:39,665 - __main__ - INFO - Starting system monitor...
2025-07-23 08:33:39,667 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-23 08:33:39,667 - __main__ - INFO - Starting cache update monitor...
2025-07-23 08:33:39,668 - __main__ - INFO - Cache update monitor started
2025-07-23 08:33:39,669 - __main__ - INFO - Cache update monitor started
2025-07-23 08:33:39,678 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:33:39,680 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:33:39,681 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:33:39,691 - __main__ - INFO - No new records to load
2025-07-23 08:33:39,692 - __main__ - ERROR - Incremental cache update process failed: not enough values to unpack (expected 3, got 2)
2025-07-23 08:33:39,692 - __main__ - WARNING - Automatic cache update failed: {'success': False, 'reason': 'Update process failed', 'error': 'not enough values to unpack (expected 3, got 2)'}
2025-07-23 08:34:39,698 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:34:39,701 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:34:39,701 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:34:39,707 - __main__ - INFO - No new records to load
2025-07-23 08:34:39,708 - __main__ - ERROR - Incremental cache update process failed: not enough values to unpack (expected 3, got 2)
2025-07-23 08:34:39,708 - __main__ - WARNING - Automatic cache update failed: {'success': False, 'reason': 'Update process failed', 'error': 'not enough values to unpack (expected 3, got 2)'}
2025-07-23 08:35:39,714 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:35:39,716 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:35:39,716 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:35:39,723 - __main__ - INFO - No new records to load
2025-07-23 08:35:39,723 - __main__ - ERROR - Incremental cache update process failed: not enough values to unpack (expected 3, got 2)
2025-07-23 08:35:39,724 - __main__ - WARNING - Automatic cache update failed: {'success': False, 'reason': 'Update process failed', 'error': 'not enough values to unpack (expected 3, got 2)'}
2025-07-23 08:35:54,155 - __main__ - INFO - Shutting down Search API...
2025-07-23 08:35:56,170 - __main__ - INFO - Cache update monitor stopped
2025-07-23 08:35:57,178 - __main__ - INFO - System monitor stopped
2025-07-23 08:35:57,180 - __main__ - INFO - Database connection closed
2025-07-23 08:37:09,212 - __main__ - INFO - Starting up Search API...
2025-07-23 08:37:09,213 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-23 08:37:09,220 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 08:37:09,221 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 08:37:12,626 - __main__ - INFO - Model loaded successfully
2025-07-23 08:37:12,627 - __main__ - INFO - Initializing database connection...
2025-07-23 08:37:12,736 - __main__ - INFO - Database connection established
2025-07-23 08:37:12,737 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-23 08:37:12,738 - __main__ - INFO - All cache files found on disk
2025-07-23 08:37:12,738 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-23 08:37:12,775 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-23 08:37:12,810 - __main__ - INFO - Loading BM25 index from disk...
2025-07-23 08:37:15,729 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-23 08:37:15,729 - __main__ - INFO - Cache loaded from disk in 2.99s
2025-07-23 08:37:15,730 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-23 08:37:16,192 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-23 08:37:16,192 - __main__ - INFO - Memory-mapped storage: True
2025-07-23 08:37:16,194 - __main__ - INFO - Starting system monitor...
2025-07-23 08:37:16,195 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-23 08:37:16,195 - __main__ - INFO - Starting cache update monitor...
2025-07-23 08:37:16,195 - __main__ - INFO - Cache update monitor started
2025-07-23 08:37:16,195 - __main__ - INFO - Cache update monitor started
2025-07-23 08:37:16,202 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:37:16,203 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:37:16,204 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:37:16,211 - __main__ - INFO - No new records to load
2025-07-23 08:37:16,212 - __main__ - INFO - No valid new records found after processing
2025-07-23 08:38:16,216 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:38:16,219 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:38:16,219 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:38:16,225 - __main__ - INFO - No new records to load
2025-07-23 08:38:16,225 - __main__ - INFO - No valid new records found after processing
2025-07-23 08:38:59,939 - __main__ - INFO - Shutting down Search API...
2025-07-23 08:39:01,951 - __main__ - INFO - Cache update monitor stopped
2025-07-23 08:39:02,964 - __main__ - INFO - System monitor stopped
2025-07-23 08:39:02,966 - __main__ - INFO - Database connection closed
2025-07-23 08:44:04,971 - __main__ - INFO - Starting up Search API...
2025-07-23 08:44:04,971 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-23 08:44:04,975 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 08:44:04,975 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 08:44:07,742 - __main__ - INFO - Model loaded successfully
2025-07-23 08:44:07,743 - __main__ - INFO - Initializing database connection...
2025-07-23 08:44:07,876 - __main__ - INFO - Database connection established
2025-07-23 08:44:07,877 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-23 08:44:07,878 - __main__ - INFO - All cache files found on disk
2025-07-23 08:44:07,878 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-23 08:44:07,917 - __main__ - INFO - Loading 393311 records from existing cache (576.1MB)
2025-07-23 08:44:07,954 - __main__ - INFO - Loading BM25 index from disk...
2025-07-23 08:44:10,832 - __main__ - INFO - BM25 index loaded: 659436 terms, 393311 documents
2025-07-23 08:44:10,832 - __main__ - INFO - Cache loaded from disk in 2.95s
2025-07-23 08:44:10,833 - __main__ - INFO - Loaded 393311 records with memory-mapped storage
2025-07-23 08:44:11,456 - __main__ - INFO - Search cache loaded: 393311 records
2025-07-23 08:44:11,457 - __main__ - INFO - Memory-mapped storage: True
2025-07-23 08:44:11,457 - __main__ - INFO - Starting system monitor...
2025-07-23 08:44:11,459 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-23 08:44:11,460 - __main__ - INFO - Starting cache update monitor...
2025-07-23 08:44:11,461 - __main__ - INFO - Cache update monitor started
2025-07-23 08:44:11,462 - __main__ - INFO - Cache update monitor started
2025-07-23 08:44:11,468 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:44:11,472 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:44:11,473 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:44:11,483 - __main__ - INFO - Loading 1 new records incrementally
2025-07-23 08:44:11,484 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-23 08:44:11,818 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-23 08:44:11,819 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-23 08:44:11,822 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-23 08:44:11,878 - __main__ - INFO - Extending embeddings from 393311 to 393312 records
2025-07-23 08:44:12,119 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-23 08:44:14,269 - __main__ - INFO - Embeddings file extended successfully to 393312 records
2025-07-23 08:44:14,270 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-23 08:44:14,276 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393312 documents
2025-07-23 08:44:20,690 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-23 08:44:21,052 - __main__ - INFO - Incremental cache update completed in 9.23s
2025-07-23 08:44:21,053 - __main__ - INFO - Cache now contains 393312 total records
2025-07-23 08:44:21,248 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393312, 'update_time_seconds': 9.229493379592896}
2025-07-23 08:44:21,249 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393312, 'update_time_seconds': 9.229493379592896, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-23 08:45:21,253 - __main__ - INFO - Found 1 new records since 2016-05-31 16:04:14.217000
2025-07-23 08:45:21,256 - __main__ - INFO - Starting incremental update for 1 new records
2025-07-23 08:45:21,256 - __main__ - INFO - Loading new records since 2016-05-31 16:04:14.217000 with chunk size 5000
2025-07-23 08:45:21,262 - __main__ - INFO - Loading 1 new records incrementally
2025-07-23 08:45:21,262 - __main__ - INFO - Processing incremental chunk 1: records 0-1
2025-07-23 08:45:21,448 - __main__ - INFO - Incremental progress: 1/1 records processed (100.0%)
2025-07-23 08:45:21,448 - __main__ - INFO - Incremental loading complete: 1 new records loaded
2025-07-23 08:45:21,449 - __main__ - INFO - Starting incremental cache update with 1 new records
2025-07-23 08:45:21,490 - __main__ - INFO - Extending embeddings from 393312 to 393313 records
2025-07-23 08:45:21,652 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-23 08:45:23,081 - __main__ - INFO - Embeddings file extended successfully to 393313 records
2025-07-23 08:45:23,082 - __main__ - INFO - Adding 1 documents to BM25 index incrementally...
2025-07-23 08:45:23,087 - __main__ - INFO - Saving updated BM25 index: 659436 terms, 393313 documents
2025-07-23 08:45:28,265 - __main__ - INFO - BM25 index updated and saved to disk
2025-07-23 08:45:28,538 - __main__ - INFO - Incremental cache update completed in 7.09s
2025-07-23 08:45:28,539 - __main__ - INFO - Cache now contains 393313 total records
2025-07-23 08:45:28,720 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1, 'total_records': 393313, 'update_time_seconds': 7.089377403259277}
2025-07-23 08:45:28,721 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1, 'total_records': 393313, 'update_time_seconds': 7.089377403259277, 'latest_entered_time': '2016-05-31 16:04:14.217000', 'memory_used_mb': 0.00146484375}
2025-07-23 08:45:38,176 - __main__ - INFO - Shutting down Search API...
2025-07-23 08:45:40,186 - __main__ - INFO - Cache update monitor stopped
2025-07-23 08:45:41,195 - __main__ - INFO - System monitor stopped
2025-07-23 08:45:41,197 - __main__ - INFO - Database connection closed
2025-07-23 08:46:38,123 - __main__ - INFO - Starting up Search API...
2025-07-23 08:46:38,123 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-23 08:46:38,126 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 08:46:38,127 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 08:46:41,004 - __main__ - INFO - Model loaded successfully
2025-07-23 08:46:41,004 - __main__ - INFO - Initializing database connection...
2025-07-23 08:46:41,140 - __main__ - INFO - Database connection established
2025-07-23 08:46:41,140 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-23 08:46:41,142 - __main__ - INFO - All cache files found on disk
2025-07-23 08:46:41,142 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-23 08:46:41,205 - __main__ - INFO - Loading 393313 records from existing cache (576.1MB)
2025-07-23 08:46:41,242 - __main__ - INFO - Loading BM25 index from disk...
2025-07-23 08:46:43,890 - __main__ - INFO - BM25 index loaded: 659436 terms, 393313 documents
2025-07-23 08:46:43,891 - __main__ - INFO - Cache loaded from disk in 2.75s
2025-07-23 08:46:43,891 - __main__ - INFO - Loaded 393313 records with memory-mapped storage
2025-07-23 08:46:44,306 - __main__ - INFO - Search cache loaded: 393313 records
2025-07-23 08:46:44,307 - __main__ - INFO - Memory-mapped storage: True
2025-07-23 08:46:44,307 - __main__ - INFO - Starting system monitor...
2025-07-23 08:46:44,309 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-23 08:46:44,310 - __main__ - INFO - Starting cache update monitor...
2025-07-23 08:46:44,311 - __main__ - INFO - Cache update monitor started
2025-07-23 08:46:44,311 - __main__ - INFO - Cache update monitor started
2025-07-23 08:48:34,881 - __main__ - INFO - Shutting down Search API...
2025-07-23 08:48:36,885 - __main__ - INFO - Cache update monitor stopped
2025-07-23 08:48:37,892 - __main__ - INFO - System monitor stopped
2025-07-23 08:48:37,895 - __main__ - INFO - Database connection closed
